<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد بطاقات الهوية</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="file"]:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        .print-btn {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin-top: 20px;
        }
        
        .print-btn:hover {
            background-color: #45a049;
        }
        
        /* Print styles */
        @media print {
            body * {
                visibility: hidden;
            }
            
            .print-area, .print-area * {
                visibility: visible;
            }
            
            .print-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            
            .container {
                display: none;
            }
        }
        
        .print-area {
            display: none;
            width: 210mm;
            height: 297mm;
            margin: 0;
            padding: 10mm;
            box-sizing: border-box;
        }
        
        .id-cards-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 5mm;
            width: 100%;
            height: 100%;
        }
        
        .id-card {
            width: 85mm;
            height: 55mm;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 5mm;
            box-sizing: border-box;
            display: flex;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .id-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
        }
        
        .card-content {
            display: flex;
            width: 100%;
            z-index: 1;
        }
        
        .photo-section {
            width: 25mm;
            height: 35mm;
            margin-left: 3mm;
            border: 1px solid #fff;
            border-radius: 4px;
            overflow: hidden;
            background: #fff;
        }
        
        .photo-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .info-section {
            flex: 1;
            padding-right: 3mm;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .card-title {
            font-size: 8pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 2mm;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .card-field {
            font-size: 7pt;
            margin-bottom: 1.5mm;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .field-label {
            font-weight: bold;
            display: inline-block;
            width: 15mm;
        }
        
        .field-value {
            font-weight: normal;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>مولد بطاقات الهوية</h1>
        
        <form id="identityForm">
            <div class="form-group">
                <label for="fullName">الاسم الكامل:</label>
                <input type="text" id="fullName" name="fullName" required>
            </div>
            
            <div class="form-group">
                <label for="photo">الصورة الشخصية:</label>
                <input type="file" id="photo" name="photo" accept="image/*" required>
            </div>
            
            <div class="form-group">
                <label for="address">العنوان:</label>
                <input type="text" id="address" name="address" required>
            </div>
            
            <div class="form-group">
                <label for="jobTitle">الوظيفة:</label>
                <input type="text" id="jobTitle" name="jobTitle" required>
            </div>
            
            <button type="button" class="print-btn" onclick="generateAndPrint()">طباعة</button>
        </form>
    </div>
    
    <div class="print-area" id="printArea">
        <div class="id-cards-grid" id="cardsGrid">
            <!-- سيتم إنشاء البطاقات هنا بواسطة JavaScript -->
        </div>
    </div>

    <script>
        let photoDataUrl = '';
        
        // معالجة رفع الصورة
        $('#photo').on('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    photoDataUrl = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        function generateAndPrint() {
            // التحقق من ملء جميع الحقول
            const fullName = $('#fullName').val().trim();
            const address = $('#address').val().trim();
            const jobTitle = $('#jobTitle').val().trim();
            
            if (!fullName || !address || !jobTitle || !photoDataUrl) {
                alert('يرجى ملء جميع الحقول ورفع الصورة');
                return;
            }
            
            // إنشاء 6 بطاقات
            const cardsGrid = $('#cardsGrid');
            cardsGrid.empty();
            
            for (let i = 0; i < 6; i++) {
                const cardHtml = `
                    <div class="id-card">
                        <div class="card-content">
                            <div class="info-section">
                                <div class="card-title">بطاقة هوية</div>
                                <div class="card-field">
                                    <span class="field-label">الاسم:</span>
                                    <span class="field-value">${fullName}</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-label">العنوان:</span>
                                    <span class="field-value">${address}</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-label">الوظيفة:</span>
                                    <span class="field-value">${jobTitle}</span>
                                </div>
                            </div>
                            <div class="photo-section">
                                <img src="${photoDataUrl}" alt="صورة شخصية">
                            </div>
                        </div>
                    </div>
                `;
                cardsGrid.append(cardHtml);
            }
            
            // عرض منطقة الطباعة وطباعة الصفحة
            $('#printArea').show();
            setTimeout(() => {
                window.print();
                $('#printArea').hide();
            }, 500);
        }
    </script>
</body>
</html>
