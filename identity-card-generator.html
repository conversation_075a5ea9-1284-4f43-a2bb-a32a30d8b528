<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>مولد بطاقات الهوية</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
      body {
        font-family: 'Arial', sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }

      .forms-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        margin-bottom: 30px;
      }

      .form-section {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        background: #f9f9f9;
      }

      .form-section h3 {
        margin: 0 0 20px 0;
        color: #333;
        text-align: center;
        background: #4caf50;
        color: white;
        padding: 10px;
        border-radius: 5px;
        margin: -20px -20px 20px -20px;
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }

      input[type='text'],
      input[type='date'],
      input[type='file'],
      select {
        width: 100%;
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        box-sizing: border-box;
      }

      input[type='text']:focus,
      input[type='date']:focus,
      input[type='file']:focus,
      select:focus {
        border-color: #4caf50;
        outline: none;
      }

      .photo-preview {
        margin-top: 10px;
        text-align: center;
      }

      .photo-preview img {
        max-width: 150px;
        max-height: 200px;
        border: 2px solid #ddd;
        border-radius: 5px;
      }

      .print-btn {
        background-color: #4caf50;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 18px;
        width: 100%;
        margin-top: 20px;
      }

      .print-btn:hover {
        background-color: #45a049;
      }

      /* Print styles */
      @media print {
        body * {
          visibility: hidden;
        }

        .print-area,
        .print-area * {
          visibility: visible;
        }

        .print-area {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }

        .container {
          display: none;
        }
      }

      .print-area {
        display: none;
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 10mm;
        box-sizing: border-box;
      }

      .id-cards-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 5mm;
        width: 100%;
        height: 100%;
        justify-items: center;
        align-items: center;
      }

      .id-card {
        width: 55mm;
        height: 85mm;
        border: none;
        border-radius: 0;
        padding: 0;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        background: white;
        color: #333;
        position: relative;
        overflow: hidden;
      }



      .card-content {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        z-index: 1;
        padding: 3mm;
        box-sizing: border-box;
      }

      .photo-section {
        width: 30mm;
        height: 40mm;
        margin: 5mm auto 3mm auto;
        border: none;
        border-radius: 0;
        overflow: hidden;
        background: #fff;
        box-shadow: none;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .photo-section img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .info-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: transparent;
        border: none;
        border-radius: 0;
        padding: 3mm;
        margin: 0;
        box-shadow: none;
        text-decoration: none;
      }



      .card-field {
        font-size: 8pt;
        margin-bottom: 2mm;
        color: #333;
        text-shadow: none;
        line-height: 1.4;
        text-align: center;
        border: none;
        text-decoration: none;
      }

      .field-value {
        font-weight: normal;
        color: #333;
        display: block;
        margin-bottom: 1mm;
        border: none;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>مولد بطاقات الهوية - 6 نماذج</h1>

      <div class="forms-container">
        <!-- النموذج الأول -->
        <div class="form-section">
          <h3>البطاقة الأولى</h3>
          <div class="form-group">
            <label for="fullName1">الاسم:</label>
            <input type="text" id="fullName1" name="fullName1" />
          </div>
          <div class="form-group">
            <label for="jobTitle1">العنوان الوظيفي:</label>
            <select id="jobTitle1" name="jobTitle1">
              <option value="">اختر...</option>
              <option value="مدير مكتب">مدير مكتب</option>
              <option value="سكرتير">سكرتير</option>
              <option value="مكتب">مكتب</option>
              <option value="مرافق">مرافق</option>
              <option value="حماية">حماية</option>
              <option value="اعلامي">اعلامي</option>
              <option value="سائق">سائق</option>
              <option value="موظف خدمات">موظف خدمات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="member1">عضو:</label>
            <select id="member1" name="member1">
              <option value="">اختر...</option>
              <option value="رئيس مجلس محافظة كركوك">
                رئيس مجلس محافظة كركوك
              </option>
              <option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>
              <option value="أحمد عبد الواحد أمين قاسم">
                أحمد عبد الواحد أمين قاسم
              </option>
              <option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>
              <option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>
              <option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>
              <option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>
              <option value="راكان سعيد علي الجبوري">
                راكان سعيد علي الجبوري
              </option>
              <option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>
              <option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>
              <option value="سوسن عبد الواحد شاكر جدوع">
                سوسن عبد الواحد شاكر جدوع
              </option>
              <option value="شوخان حسيب حسين عبد الله">
                شوخان حسيب حسين عبد الله
              </option>
              <option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>
              <option value="عبدالله مير ويس الشيخاني">
                عبدالله مير ويس الشيخاني
              </option>
              <option value="نشات شاهويز خورشيد علي">
                نشات شاهويز خورشيد علي
              </option>
              <option value="هوشيار هجران نجم الدين خليل">
                هوشيار هجران نجم الدين خليل
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="badgeNumber1">رقم الباج:</label>
            <input type="text" id="badgeNumber1" name="badgeNumber1" />
          </div>
          <div class="form-group">
            <label for="expiryDate1">تاريخ النفاذ:</label>
            <input type="date" id="expiryDate1" name="expiryDate1" />
          </div>
          <div class="form-group">
            <label for="photo1">الصورة الشخصية:</label>
            <input type="file" id="photo1" name="photo1" accept="image/*" />
            <div class="photo-preview" id="photoPreview1"></div>
          </div>
        </div>

        <!-- النموذج الثاني -->
        <div class="form-section">
          <h3>البطاقة الثانية</h3>
          <div class="form-group">
            <label for="fullName2">الاسم:</label>
            <input type="text" id="fullName2" name="fullName2" />
          </div>
          <div class="form-group">
            <label for="jobTitle2">العنوان الوظيفي:</label>
            <select id="jobTitle2" name="jobTitle2">
              <option value="">اختر...</option>
              <option value="مدير مكتب">مدير مكتب</option>
              <option value="سكرتير">سكرتير</option>
              <option value="مكتب">مكتب</option>
              <option value="مرافق">مرافق</option>
              <option value="حماية">حماية</option>
              <option value="اعلامي">اعلامي</option>
              <option value="سائق">سائق</option>
              <option value="موظف خدمات">موظف خدمات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="member2">عضو:</label>
            <select id="member2" name="member2">
              <option value="">اختر...</option>
              <option value="رئيس مجلس محافظة كركوك">
                رئيس مجلس محافظة كركوك
              </option>
              <option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>
              <option value="أحمد عبد الواحد أمين قاسم">
                أحمد عبد الواحد أمين قاسم
              </option>
              <option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>
              <option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>
              <option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>
              <option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>
              <option value="راكان سعيد علي الجبوري">
                راكان سعيد علي الجبوري
              </option>
              <option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>
              <option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>
              <option value="سوسن عبد الواحد شاكر جدوع">
                سوسن عبد الواحد شاكر جدوع
              </option>
              <option value="شوخان حسيب حسين عبد الله">
                شوخان حسيب حسين عبد الله
              </option>
              <option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>
              <option value="عبدالله مير ويس الشيخاني">
                عبدالله مير ويس الشيخاني
              </option>
              <option value="نشات شاهويز خورشيد علي">
                نشات شاهويز خورشيد علي
              </option>
              <option value="هوشيار هجران نجم الدين خليل">
                هوشيار هجران نجم الدين خليل
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="badgeNumber2">رقم الباج:</label>
            <input type="text" id="badgeNumber2" name="badgeNumber2" />
          </div>
          <div class="form-group">
            <label for="expiryDate2">تاريخ النفاذ:</label>
            <input type="date" id="expiryDate2" name="expiryDate2" />
          </div>
          <div class="form-group">
            <label for="photo2">الصورة الشخصية:</label>
            <input type="file" id="photo2" name="photo2" accept="image/*" />
            <div class="photo-preview" id="photoPreview2"></div>
          </div>
        </div>

        <!-- النموذج الثالث -->
        <div class="form-section">
          <h3>البطاقة الثالثة</h3>
          <div class="form-group">
            <label for="fullName3">الاسم:</label>
            <input type="text" id="fullName3" name="fullName3" />
          </div>
          <div class="form-group">
            <label for="jobTitle3">العنوان الوظيفي:</label>
            <select id="jobTitle3" name="jobTitle3">
              <option value="">اختر...</option>
              <option value="مدير مكتب">مدير مكتب</option>
              <option value="سكرتير">سكرتير</option>
              <option value="مكتب">مكتب</option>
              <option value="مرافق">مرافق</option>
              <option value="حماية">حماية</option>
              <option value="اعلامي">اعلامي</option>
              <option value="سائق">سائق</option>
              <option value="موظف خدمات">موظف خدمات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="member3">عضو:</label>
            <select id="member3" name="member3">
              <option value="">اختر...</option>
              <option value="رئيس مجلس محافظة كركوك">رئيس مجلس محافظة كركوك</option>
              <option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>
              <option value="أحمد عبد الواحد أمين قاسم">أحمد عبد الواحد أمين قاسم</option>
              <option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>
              <option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>
              <option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>
              <option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>
              <option value="راكان سعيد علي الجبوري">راكان سعيد علي الجبوري</option>
              <option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>
              <option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>
              <option value="سوسن عبد الواحد شاكر جدوع">سوسن عبد الواحد شاكر جدوع</option>
              <option value="شوخان حسيب حسين عبد الله">شوخان حسيب حسين عبد الله</option>
              <option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>
              <option value="عبدالله مير ويس الشيخاني">عبدالله مير ويس الشيخاني</option>
              <option value="نشات شاهويز خورشيد علي">نشات شاهويز خورشيد علي</option>
              <option value="هوشيار هجران نجم الدين خليل">هوشيار هجران نجم الدين خليل</option>
            </select>
          </div>
          <div class="form-group">
            <label for="badgeNumber3">رقم الباج:</label>
            <input type="text" id="badgeNumber3" name="badgeNumber3" />
          </div>
          <div class="form-group">
            <label for="expiryDate3">تاريخ النفاذ:</label>
            <input type="date" id="expiryDate3" name="expiryDate3" />
          </div>
          <div class="form-group">
            <label for="photo3">الصورة الشخصية:</label>
            <input type="file" id="photo3" name="photo3" accept="image/*" />
            <div class="photo-preview" id="photoPreview3"></div>
          </div>
        </div>

        <!-- النموذج الرابع -->
        <div class="form-section">
          <h3>البطاقة الرابعة</h3>
          <div class="form-group">
            <label for="fullName4">الاسم:</label>
            <input type="text" id="fullName4" name="fullName4" />
          </div>
          <div class="form-group">
            <label for="jobTitle4">العنوان الوظيفي:</label>
            <select id="jobTitle4" name="jobTitle4">
              <option value="">اختر...</option>
              <option value="مدير مكتب">مدير مكتب</option>
              <option value="سكرتير">سكرتير</option>
              <option value="مكتب">مكتب</option>
              <option value="مرافق">مرافق</option>
              <option value="حماية">حماية</option>
              <option value="اعلامي">اعلامي</option>
              <option value="سائق">سائق</option>
              <option value="موظف خدمات">موظف خدمات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="member4">عضو:</label>
            <select id="member4" name="member4">
              <option value="">اختر...</option>
              <option value="رئيس مجلس محافظة كركوك">رئيس مجلس محافظة كركوك</option>
              <option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>
              <option value="أحمد عبد الواحد أمين قاسم">أحمد عبد الواحد أمين قاسم</option>
              <option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>
              <option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>
              <option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>
              <option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>
              <option value="راكان سعيد علي الجبوري">راكان سعيد علي الجبوري</option>
              <option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>
              <option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>
              <option value="سوسن عبد الواحد شاكر جدوع">سوسن عبد الواحد شاكر جدوع</option>
              <option value="شوخان حسيب حسين عبد الله">شوخان حسيب حسين عبد الله</option>
              <option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>
              <option value="عبدالله مير ويس الشيخاني">عبدالله مير ويس الشيخاني</option>
              <option value="نشات شاهويز خورشيد علي">نشات شاهويز خورشيد علي</option>
              <option value="هوشيار هجران نجم الدين خليل">هوشيار هجران نجم الدين خليل</option>
            </select>
          </div>
          <div class="form-group">
            <label for="badgeNumber4">رقم الباج:</label>
            <input type="text" id="badgeNumber4" name="badgeNumber4" />
          </div>
          <div class="form-group">
            <label for="expiryDate4">تاريخ النفاذ:</label>
            <input type="date" id="expiryDate4" name="expiryDate4" />
          </div>
          <div class="form-group">
            <label for="photo4">الصورة الشخصية:</label>
            <input type="file" id="photo4" name="photo4" accept="image/*" />
            <div class="photo-preview" id="photoPreview4"></div>
          </div>
        </div>

        <!-- النموذج الخامس -->
        <div class="form-section">
          <h3>البطاقة الخامسة</h3>
          <div class="form-group">
            <label for="fullName5">الاسم:</label>
            <input type="text" id="fullName5" name="fullName5" />
          </div>
          <div class="form-group">
            <label for="jobTitle5">العنوان الوظيفي:</label>
            <select id="jobTitle5" name="jobTitle5">
              <option value="">اختر...</option>
              <option value="مدير مكتب">مدير مكتب</option>
              <option value="سكرتير">سكرتير</option>
              <option value="مكتب">مكتب</option>
              <option value="مرافق">مرافق</option>
              <option value="حماية">حماية</option>
              <option value="اعلامي">اعلامي</option>
              <option value="سائق">سائق</option>
              <option value="موظف خدمات">موظف خدمات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="member5">عضو:</label>
            <select id="member5" name="member5">
              <option value="">اختر...</option>
              <option value="رئيس مجلس محافظة كركوك">رئيس مجلس محافظة كركوك</option>
              <option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>
              <option value="أحمد عبد الواحد أمين قاسم">أحمد عبد الواحد أمين قاسم</option>
              <option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>
              <option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>
              <option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>
              <option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>
              <option value="راكان سعيد علي الجبوري">راكان سعيد علي الجبوري</option>
              <option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>
              <option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>
              <option value="سوسن عبد الواحد شاكر جدوع">سوسن عبد الواحد شاكر جدوع</option>
              <option value="شوخان حسيب حسين عبد الله">شوخان حسيب حسين عبد الله</option>
              <option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>
              <option value="عبدالله مير ويس الشيخاني">عبدالله مير ويس الشيخاني</option>
              <option value="نشات شاهويز خورشيد علي">نشات شاهويز خورشيد علي</option>
              <option value="هوشيار هجران نجم الدين خليل">هوشيار هجران نجم الدين خليل</option>
            </select>
          </div>
          <div class="form-group">
            <label for="badgeNumber5">رقم الباج:</label>
            <input type="text" id="badgeNumber5" name="badgeNumber5" />
          </div>
          <div class="form-group">
            <label for="expiryDate5">تاريخ النفاذ:</label>
            <input type="date" id="expiryDate5" name="expiryDate5" />
          </div>
          <div class="form-group">
            <label for="photo5">الصورة الشخصية:</label>
            <input type="file" id="photo5" name="photo5" accept="image/*" />
            <div class="photo-preview" id="photoPreview5"></div>
          </div>
        </div>

        <!-- النموذج السادس -->
        <div class="form-section">
          <h3>البطاقة السادسة</h3>
          <div class="form-group">
            <label for="fullName6">الاسم:</label>
            <input type="text" id="fullName6" name="fullName6" />
          </div>
          <div class="form-group">
            <label for="jobTitle6">العنوان الوظيفي:</label>
            <select id="jobTitle6" name="jobTitle6">
              <option value="">اختر...</option>
              <option value="مدير مكتب">مدير مكتب</option>
              <option value="سكرتير">سكرتير</option>
              <option value="مكتب">مكتب</option>
              <option value="مرافق">مرافق</option>
              <option value="حماية">حماية</option>
              <option value="اعلامي">اعلامي</option>
              <option value="سائق">سائق</option>
              <option value="موظف خدمات">موظف خدمات</option>
            </select>
          </div>
          <div class="form-group">
            <label for="member6">عضو:</label>
            <select id="member6" name="member6">
              <option value="">اختر...</option>
              <option value="رئيس مجلس محافظة كركوك">رئيس مجلس محافظة كركوك</option>
              <option value="أحمد رمزي كريم أحمد">أحمد رمزي كريم أحمد</option>
              <option value="أحمد عبد الواحد أمين قاسم">أحمد عبد الواحد أمين قاسم</option>
              <option value="أحمد فاتح مصطفى أحمد">أحمد فاتح مصطفى أحمد</option>
              <option value="انجيل زيا شيبا زيا">انجيل زيا شيبا زيا</option>
              <option value="بروين فاتح حميد عارف">بروين فاتح حميد عارف</option>
              <option value="حسن مجيد رشيد حسين">حسن مجيد رشيد حسين</option>
              <option value="راكان سعيد علي الجبوري">راكان سعيد علي الجبوري</option>
              <option value="رعد صالح حسين موسى">رعد صالح حسين موسى</option>
              <option value="سلوى احمد ميدان حسين">سلوى احمد ميدان حسين</option>
              <option value="سوسن عبد الواحد شاكر جدوع">سوسن عبد الواحد شاكر جدوع</option>
              <option value="شوخان حسيب حسين عبد الله">شوخان حسيب حسين عبد الله</option>
              <option value="ظاهر أنور عاصي حسين">ظاهر أنور عاصي حسين</option>
              <option value="عبدالله مير ويس الشيخاني">عبدالله مير ويس الشيخاني</option>
              <option value="نشات شاهويز خورشيد علي">نشات شاهويز خورشيد علي</option>
              <option value="هوشيار هجران نجم الدين خليل">هوشيار هجران نجم الدين خليل</option>
            </select>
          </div>
          <div class="form-group">
            <label for="badgeNumber6">رقم الباج:</label>
            <input type="text" id="badgeNumber6" name="badgeNumber6" />
          </div>
          <div class="form-group">
            <label for="expiryDate6">تاريخ النفاذ:</label>
            <input type="date" id="expiryDate6" name="expiryDate6" />
          </div>
          <div class="form-group">
            <label for="photo6">الصورة الشخصية:</label>
            <input type="file" id="photo6" name="photo6" accept="image/*" />
            <div class="photo-preview" id="photoPreview6"></div>
          </div>
        </div>
      </div>

      <button type="button" class="print-btn" onclick="generateAndPrint()">
        طباعة
      </button>
      </div>
    </div>

    <div class="print-area" id="printArea">
      <div class="id-cards-grid" id="cardsGrid">
        <!-- سيتم إنشاء البطاقات هنا بواسطة JavaScript -->
      </div>
    </div>

    <script>
      let photoDataUrls = ['', '', '', '', '', ''];

      // معالجة رفع الصور للنماذج الستة
      for (let i = 1; i <= 6; i++) {
        $(`#photo${i}`).on('change', function (e) {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
              photoDataUrls[i - 1] = e.target.result;
              // عرض الصورة في المعاينة
              $(`#photoPreview${i}`).html(
                `<img src="${e.target.result}" alt="معاينة الصورة">`
              );
            };
            reader.readAsDataURL(file);
          }
        });
      }

      function generateAndPrint() {
        // جمع بيانات النماذج الستة
        const cardsData = [];

        for (let i = 1; i <= 6; i++) {
          const fullName = $(`#fullName${i}`).val().trim();
          const jobTitle = $(`#jobTitle${i}`).val().trim();
          const member = $(`#member${i}`).val().trim();
          const badgeNumber = $(`#badgeNumber${i}`).val().trim();
          const expiryDate = $(`#expiryDate${i}`).val().trim();
          const photo = photoDataUrls[i - 1];

          cardsData.push({
            fullName: fullName,
            jobTitle: jobTitle,
            member: member,
            badgeNumber: badgeNumber,
            expiryDate: expiryDate,
            photo: photo,
          });
        }

        // إنشاء 6 بطاقات
        const cardsGrid = $('#cardsGrid');
        cardsGrid.empty();

        for (let i = 0; i < 6; i++) {
          const data = cardsData[i];

          // إذا كانت البيانات فارغة، عرض بطاقة فارغة
          const cardHtml = `
                    <div class="id-card">
                        <div class="card-content">
                            <div class="photo-section">
                                ${
                                  data.photo
                                    ? `<img src="${data.photo}" alt="صورة شخصية">`
                                    : '<div style="display:flex;align-items:center;justify-content:center;height:100%;color:#999;font-size:10px;">صورة</div>'
                                }
                            </div>
                            <div class="info-section">

                                <div class="card-field">
                                    <span class="field-value">${data.fullName || '_______________'}</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-value">${data.jobTitle || '_______________'}</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-value">${data.member || '_______________'}</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-value">${data.badgeNumber || '_______________'}</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-value">${data.expiryDate || '_______________'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
          cardsGrid.append(cardHtml);
        }

        // عرض منطقة الطباعة وطباعة الصفحة
        $('#printArea').show();
        setTimeout(() => {
          window.print();
          $('#printArea').hide();
        }, 500);
      }
    </script>
  </body>
</html>
