<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>مولد بطاقات الهوية</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
      body {
        font-family: 'Arial', sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }

      .forms-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
        margin-bottom: 30px;
      }

      .form-section {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        background: #f9f9f9;
      }

      .form-section h3 {
        margin: 0 0 20px 0;
        color: #333;
        text-align: center;
        background: #4caf50;
        color: white;
        padding: 10px;
        border-radius: 5px;
        margin: -20px -20px 20px -20px;
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }

      input[type='text'],
      input[type='file'] {
        width: 100%;
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        box-sizing: border-box;
      }

      input[type='text']:focus,
      input[type='file']:focus {
        border-color: #4caf50;
        outline: none;
      }

      .print-btn {
        background-color: #4caf50;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 18px;
        width: 100%;
        margin-top: 20px;
      }

      .print-btn:hover {
        background-color: #45a049;
      }

      /* Print styles */
      @media print {
        body * {
          visibility: hidden;
        }

        .print-area,
        .print-area * {
          visibility: visible;
        }

        .print-area {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }

        .container {
          display: none;
        }
      }

      .print-area {
        display: none;
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 10mm;
        box-sizing: border-box;
      }

      .id-cards-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(3, 1fr);
        gap: 5mm;
        width: 100%;
        height: 100%;
      }

      .id-card {
        width: 85mm;
        height: 55mm;
        border: 2px solid #333;
        border-radius: 8px;
        padding: 5mm;
        box-sizing: border-box;
        display: flex;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
        overflow: hidden;
      }

      .id-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 6px;
      }

      .card-content {
        display: flex;
        width: 100%;
        z-index: 1;
      }

      .photo-section {
        width: 25mm;
        height: 35mm;
        margin-left: 3mm;
        border: 1px solid #fff;
        border-radius: 4px;
        overflow: hidden;
        background: #fff;
      }

      .photo-section img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .info-section {
        flex: 1;
        padding-right: 3mm;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .card-title {
        font-size: 8pt;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2mm;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .card-field {
        font-size: 7pt;
        margin-bottom: 1.5mm;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
      }

      .field-label {
        font-weight: bold;
        display: inline-block;
        width: 15mm;
      }

      .field-value {
        font-weight: normal;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>مولد بطاقات الهوية - 6 نماذج</h1>

      <div class="forms-container">
        <!-- النموذج الأول -->
        <div class="form-section">
          <h3>البطاقة الأولى</h3>
          <div class="form-group">
            <label for="fullName1">الاسم الكامل:</label>
            <input type="text" id="fullName1" name="fullName1" />
          </div>
          <div class="form-group">
            <label for="photo1">الصورة الشخصية:</label>
            <input type="file" id="photo1" name="photo1" accept="image/*" />
          </div>
          <div class="form-group">
            <label for="address1">العنوان:</label>
            <input type="text" id="address1" name="address1" />
          </div>
          <div class="form-group">
            <label for="jobTitle1">الوظيفة:</label>
            <input type="text" id="jobTitle1" name="jobTitle1" />
          </div>
        </div>

        <!-- النموذج الثاني -->
        <div class="form-section">
          <h3>البطاقة الثانية</h3>
          <div class="form-group">
            <label for="fullName2">الاسم الكامل:</label>
            <input type="text" id="fullName2" name="fullName2" />
          </div>
          <div class="form-group">
            <label for="photo2">الصورة الشخصية:</label>
            <input type="file" id="photo2" name="photo2" accept="image/*" />
          </div>
          <div class="form-group">
            <label for="address2">العنوان:</label>
            <input type="text" id="address2" name="address2" />
          </div>
          <div class="form-group">
            <label for="jobTitle2">الوظيفة:</label>
            <input type="text" id="jobTitle2" name="jobTitle2" />
          </div>
        </div>

        <!-- النموذج الثالث -->
        <div class="form-section">
          <h3>البطاقة الثالثة</h3>
          <div class="form-group">
            <label for="fullName3">الاسم الكامل:</label>
            <input type="text" id="fullName3" name="fullName3" />
          </div>
          <div class="form-group">
            <label for="photo3">الصورة الشخصية:</label>
            <input type="file" id="photo3" name="photo3" accept="image/*" />
          </div>
          <div class="form-group">
            <label for="address3">العنوان:</label>
            <input type="text" id="address3" name="address3" />
          </div>
          <div class="form-group">
            <label for="jobTitle3">الوظيفة:</label>
            <input type="text" id="jobTitle3" name="jobTitle3" />
          </div>
        </div>

        <!-- النموذج الرابع -->
        <div class="form-section">
          <h3>البطاقة الرابعة</h3>
          <div class="form-group">
            <label for="fullName4">الاسم الكامل:</label>
            <input type="text" id="fullName4" name="fullName4" />
          </div>
          <div class="form-group">
            <label for="photo4">الصورة الشخصية:</label>
            <input type="file" id="photo4" name="photo4" accept="image/*" />
          </div>
          <div class="form-group">
            <label for="address4">العنوان:</label>
            <input type="text" id="address4" name="address4" />
          </div>
          <div class="form-group">
            <label for="jobTitle4">الوظيفة:</label>
            <input type="text" id="jobTitle4" name="jobTitle4" />
          </div>
        </div>

        <!-- النموذج الخامس -->
        <div class="form-section">
          <h3>البطاقة الخامسة</h3>
          <div class="form-group">
            <label for="fullName5">الاسم الكامل:</label>
            <input type="text" id="fullName5" name="fullName5" />
          </div>
          <div class="form-group">
            <label for="photo5">الصورة الشخصية:</label>
            <input type="file" id="photo5" name="photo5" accept="image/*" />
          </div>
          <div class="form-group">
            <label for="address5">العنوان:</label>
            <input type="text" id="address5" name="address5" />
          </div>
          <div class="form-group">
            <label for="jobTitle5">الوظيفة:</label>
            <input type="text" id="jobTitle5" name="jobTitle5" />
          </div>
        </div>

        <!-- النموذج السادس -->
        <div class="form-section">
          <h3>البطاقة السادسة</h3>
          <div class="form-group">
            <label for="fullName6">الاسم الكامل:</label>
            <input type="text" id="fullName6" name="fullName6" />
          </div>
          <div class="form-group">
            <label for="photo6">الصورة الشخصية:</label>
            <input type="file" id="photo6" name="photo6" accept="image/*" />
          </div>
          <div class="form-group">
            <label for="address6">العنوان:</label>
            <input type="text" id="address6" name="address6" />
          </div>
          <div class="form-group">
            <label for="jobTitle6">الوظيفة:</label>
            <input type="text" id="jobTitle6" name="jobTitle6" />
          </div>
        </div>
      </div>

      <button type="button" class="print-btn" onclick="generateAndPrint()">
        طباعة
      </button>
    </div>

    <div class="print-area" id="printArea">
      <div class="id-cards-grid" id="cardsGrid">
        <!-- سيتم إنشاء البطاقات هنا بواسطة JavaScript -->
      </div>
    </div>

    <script>
      let photoDataUrls = ['', '', '', '', '', ''];

      // معالجة رفع الصور للنماذج الستة
      for (let i = 1; i <= 6; i++) {
        $(`#photo${i}`).on('change', function (e) {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
              photoDataUrls[i - 1] = e.target.result;
            };
            reader.readAsDataURL(file);
          }
        });
      }

      function generateAndPrint() {
        // جمع بيانات النماذج الستة
        const cardsData = [];

        for (let i = 1; i <= 6; i++) {
          const fullName = $(`#fullName${i}`).val().trim();
          const address = $(`#address${i}`).val().trim();
          const jobTitle = $(`#jobTitle${i}`).val().trim();
          const photo = photoDataUrls[i - 1];

          cardsData.push({
            fullName: fullName,
            address: address,
            jobTitle: jobTitle,
            photo: photo,
          });
        }

        // إنشاء 6 بطاقات
        const cardsGrid = $('#cardsGrid');
        cardsGrid.empty();

        for (let i = 0; i < 6; i++) {
          const data = cardsData[i];

          // إذا كانت البيانات فارغة، عرض بطاقة فارغة
          const cardHtml = `
                    <div class="id-card">
                        <div class="card-content">
                            <div class="info-section">
                                <div class="card-title">بطاقة هوية</div>
                                <div class="card-field">
                                    <span class="field-label">الاسم:</span>
                                    <span class="field-value">${
                                      data.fullName || '_______________'
                                    }</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-label">العنوان:</span>
                                    <span class="field-value">${
                                      data.address || '_______________'
                                    }</span>
                                </div>
                                <div class="card-field">
                                    <span class="field-label">الوظيفة:</span>
                                    <span class="field-value">${
                                      data.jobTitle || '_______________'
                                    }</span>
                                </div>
                            </div>
                            <div class="photo-section">
                                ${
                                  data.photo
                                    ? `<img src="${data.photo}" alt="صورة شخصية">`
                                    : '<div style="display:flex;align-items:center;justify-content:center;height:100%;color:#999;font-size:10px;">صورة</div>'
                                }
                            </div>
                        </div>
                    </div>
                `;
          cardsGrid.append(cardHtml);
        }

        // عرض منطقة الطباعة وطباعة الصفحة
        $('#printArea').show();
        setTimeout(() => {
          window.print();
          $('#printArea').hide();
        }, 500);
      }
    </script>
  </body>
</html>
